import SwiftUI
import CloudKit

@main
struct CardsCastleApp: App {
    let persistenceController = PersistenceController.shared
    @State private var showingOnboarding = !UserDefaults.standard.bool(forKey: "HasCompletedOnboarding")

    var body: some Scene {
        WindowGroup {
            ZStack {
                ContentView()
                    .environment(\.managedObjectContext, persistenceController.container.viewContext)
                
                if showingOnboarding {
                    OnboardingView(isShowing: $showingOnboarding)
                        .transition(.asymmetric(
                            insertion: .opacity,
                            removal: .move(edge: .bottom).combined(with: .opacity)
                        ))
                        .zIndex(1)
                }
            }
            .animation(.easeInOut(duration: 0.5), value: showingOnboarding)
        }
    }
}
import SwiftUI
import AVFoundation
import Photos
import Contacts

struct PermissionStatusView: View {
    @StateObject private var permissionsManager = PermissionsManager()
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("App Permissions")) {
                    PermissionRow(
                        icon: "camera",
                        title: "Camera",
                        description: "Required to scan business cards",
                        status: permissionsManager.cameraStatus,
                        onRequest: {
                            permissionsManager.requestCameraPermission { _ in }
                        }
                    )
                    
                    PermissionRow(
                        icon: "photo",
                        title: "Photo Library",
                        description: "Required to select images for scanning",
                        status: permissionsManager.photoLibraryStatus,
                        onRequest: {
                            permissionsManager.requestPhotoLibraryPermission { _ in }
                        }
                    )
                    
                    ContactsPermissionRow(
                        status: permissionsManager.contactsStatus,
                        onRequest: {
                            permissionsManager.requestContactsPermission { _ in }
                        }
                    )
                }
                
                Section(footer: Text("CardsCastle uses these permissions to provide core functionality. You can manage these permissions in Settings → Privacy & Security.")) {
                    EmptyView()
                }
            }
            .navigationTitle("Permissions")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .onAppear {
                updatePermissionStatuses()
            }
            .permissionAlerts(permissionsManager)
        }
    }
    
    private func updatePermissionStatuses() {
        permissionsManager.cameraStatus = AVCaptureDevice.authorizationStatus(for: .video)
        permissionsManager.photoLibraryStatus = PHPhotoLibrary.authorizationStatus()
        permissionsManager.contactsStatus = CNContactStore.authorizationStatus(for: .contacts)
    }
}

struct PermissionRow: View {
    let icon: String
    let title: String
    let description: String
    let status: AVAuthorizationStatus
    let onRequest: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            statusView
        }
        .padding(.vertical, 4)
    }
    
    @ViewBuilder
    private var statusView: some View {
        switch status {
        case .authorized:
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
        case .denied, .restricted:
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            .font(.caption)
            .foregroundColor(.blue)
        case .notDetermined:
            Button("Allow") {
                onRequest()
            }
            .font(.caption)
            .foregroundColor(.blue)
        @unknown default:
            Image(systemName: "questionmark.circle")
                .foregroundColor(.orange)
        }
    }
}

struct ContactsPermissionRow: View {
    let status: CNAuthorizationStatus
    let onRequest: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: "person.crop.circle.badge.plus")
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Contacts")
                    .font(.headline)
                
                Text("Required to export to iOS Contacts app")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            contactsStatusView
        }
        .padding(.vertical, 4)
    }
    
    @ViewBuilder
    private var contactsStatusView: some View {
        switch status {
        case .authorized:
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
        case .denied, .restricted:
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            .font(.caption)
            .foregroundColor(.blue)
        case .notDetermined:
            Button("Allow") {
                onRequest()
            }
            .font(.caption)
            .foregroundColor(.blue)
        @unknown default:
            Image(systemName: "questionmark.circle")
                .foregroundColor(.orange)
        }
    }
}

#Preview {
    PermissionStatusView()
}
import SwiftUI

struct CategoriesView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Category.name, ascending: true)],
        animation: .default)
    private var categories: FetchedResults<Category>
    
    @State private var showingAddCategory = false
    
    var body: some View {
        NavigationView {
            List {
                ForEach(categories, id: \.self) { category in
                    CategoryRowView(category: category)
                }
                .onDelete(perform: deleteCategories)
            }
            .navigationTitle("Categories")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingAddCategory = true
                    }) {
                        Label("Add Category", systemImage: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddCategory) {
                AddCategoryView()
            }
            .onAppear {
                if categories.isEmpty {
                    Category.createDefaultCategories(in: viewContext)
                }
            }
        }
    }
    
    private func deleteCategories(offsets: IndexSet) {
        withAnimation {
            offsets.map { categories[$0] }.forEach { category in
                for card in category.businessCardsArray {
                    card.category = nil
                }
                viewContext.delete(category)
            }
            
            do {
                try viewContext.save()
            } catch {
                let nsError = error as NSError
                print("Failed to delete category: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

struct CategoryRowView: View {
    @ObservedObject var category: Category
    
    var body: some View {
        HStack {
            Circle()
                .fill(category.categoryColor)
                .frame(width: 20, height: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(category.wrappedName)
                    .font(.headline)
                
                Text("\(category.cardCount) card\(category.cardCount == 1 ? "" : "s")")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct AddCategoryView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) var presentationMode
    
    @State private var name = ""
    @State private var selectedColor = "blue"
    
    private let availableColors = [
        ("blue", Color.blue),
        ("green", Color.green),
        ("red", Color.red),
        ("orange", Color.orange),
        ("purple", Color.purple),
        ("yellow", Color.yellow),
        ("pink", Color.pink)
    ]
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Category Information")) {
                    TextField("Category Name", text: $name)
                }
                
                Section(header: Text("Color")) {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 20) {
                        ForEach(availableColors, id: \.0) { colorName, color in
                            Button(action: {
                                selectedColor = colorName
                            }) {
                                Circle()
                                    .fill(color)
                                    .frame(width: 40, height: 40)
                                    .overlay(
                                        Circle()
                                            .stroke(Color.primary, lineWidth: selectedColor == colorName ? 3 : 0)
                                    )
                            }
                        }
                    }
                    .padding(.vertical)
                }
            }
            .navigationTitle("Add Category")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveCategory()
                    }
                    .disabled(name.isEmpty)
                }
            }
        }
    }
    
    private func saveCategory() {
        withAnimation {
            let newCategory = Category(context: viewContext)
            newCategory.name = name
            newCategory.color = selectedColor
            
            do {
                try viewContext.save()
                presentationMode.wrappedValue.dismiss()
            } catch {
                let nsError = error as NSError
                print("Failed to save category: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

#Preview {
    CategoriesView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
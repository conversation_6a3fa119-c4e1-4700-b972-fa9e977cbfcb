# CardsCastle - Business Card Manager

A native iOS application for managing business cards with local storage and iCloud sync capabilities.

## Features

### Core Functionality
- **Business Card Storage**: Save and organize contact information including name, company, phone, email, address, website, and notes
- **Categorization**: Organize cards into custom categories (Clients, Vendors, Networking Contacts, Prospects, etc.)
- **Search & Filter**: Find specific cards by name, email, company, or filter by category
- **CRUD Operations**: Create, read, update, and delete business cards with ease

### Advanced Features
- **OCR Scanning**: Use camera or photo library to scan business cards and automatically extract contact information using Apple's Vision framework
- **Export Options**: Export cards in multiple formats:
  - vCard (.vcf) - Standard contact format
  - CSV - For spreadsheet applications
  - iOS Contacts - Direct export to device contacts
- **Cloud Sync**: Automatic synchronization across devices using CloudKit
- **Internationalization**: Full support for English, Arabic, and French languages
- **Offline Support**: Full functionality when offline, with sync when connection is restored

## Technical Stack

- **Platform**: iOS 17.0+
- **Language**: Swift 5.0
- **UI Framework**: SwiftUI
- **Data Persistence**: Core Data with CloudKit integration
- **Computer Vision**: Vision framework for OCR
- **Architecture**: MVVM pattern with SwiftUI

## Project Structure

```
CardsCastle/
├── CardsCastle.xcodeproj/          # Xcode project configuration
├── CardsCastle/
│   ├── CardsCastleApp.swift        # App entry point
│   ├── ContentView.swift           # Main UI with tab navigation
│   ├── Models/                     # Core Data models
│   │   ├── BusinessCard.swift      # Business card entity
│   │   ├── Category.swift          # Category entity
│   │   └── PersistenceController.swift # Core Data stack
│   ├── Views/                      # SwiftUI views
│   │   ├── BusinessCardDetailView.swift
│   │   ├── CategoriesView.swift
│   │   ├── ScanBusinessCardView.swift
│   │   └── ExportView.swift
│   ├── Services/                   # Business logic services
│   │   ├── BusinessCardScanner.swift
│   │   └── ContactExportService.swift
│   ├── Extensions/                 # Swift extensions
│   │   └── String+Localization.swift
│   ├── Localizable.strings         # English localization
│   ├── ar.lproj/                   # Arabic localization
│   ├── fr.lproj/                   # French localization
│   ├── CardsCastle.xcdatamodeld/   # Core Data model
│   ├── Assets.xcassets/            # App icons and images
│   ├── Info.plist                  # App configuration
│   └── Preview Content/            # SwiftUI previews
└── README.md                       # This file
```

## Setup Instructions

### Prerequisites
- Xcode 15.0 or later
- iOS 17.0+ deployment target
- Apple Developer account (for CloudKit and device testing)

### Installation
1. Clone or download the project to your local machine
2. Open `CardsCastle.xcodeproj` in Xcode
3. Configure your Apple Developer team in project settings
4. Enable CloudKit capability in the project
5. Build and run on simulator or device

### CloudKit Configuration
1. In Xcode, go to Project Settings → Signing & Capabilities
2. Add CloudKit capability
3. Ensure your Apple ID is signed in
4. The app will automatically create the necessary CloudKit schema on first run

## Usage Guide

### Adding Business Cards
1. **Manual Entry**: Tap the + button and select "Add Manually"
2. **OCR Scanning**: Tap the + button and select "Scan Business Card"
   - Take a photo or choose from library
   - Review and confirm extracted information
   - Save to your collection

### Managing Categories
1. Go to the Categories tab
2. Tap + to create new categories
3. Choose colors and names for organization
4. Assign cards to categories when creating or editing

### Searching and Filtering
1. Use the search bar to find cards by name, email, or company
2. Tap the filter button to filter by category
3. Clear filters to see all cards

### Exporting Data
1. Tap the menu button (⋯) in the Cards tab
2. Select "Export Cards"
3. Choose your preferred format:
   - **vCard**: Compatible with most contact applications
   - **CSV**: For spreadsheet analysis
   - **iOS Contacts**: Direct export to device contacts

## Permissions

The app requires the following permissions:
- **Camera**: For scanning business cards
- **Photo Library**: For selecting images to scan
- **Contacts**: For exporting to iOS Contacts app

## Localization

The app supports three languages:
- **English** (Default)
- **Arabic** (العربية)
- **French** (Français)

Language is automatically detected from device settings.

## Data Privacy

- All data is stored locally using Core Data
- iCloud sync is optional and uses Apple's secure CloudKit
- No third-party analytics or tracking
- Camera permissions only used for OCR scanning
- Contact permissions only used for export functionality

## Development Notes

### Core Data Models
- **BusinessCard**: Main entity with relationships to Category
- **Category**: Organizes cards with color coding
- Both entities support CloudKit synchronization

### Key Technologies
- **Vision Framework**: OCR text recognition from images
- **CloudKit**: Cross-device synchronization
- **SwiftUI**: Modern declarative UI framework
- **Core Data**: Local data persistence

### Architecture Patterns
- MVVM (Model-View-ViewModel) with SwiftUI
- ObservableObject for state management
- Repository pattern for data access

## Future Enhancements

Potential features for future versions:
- Bulk import from CSV/vCard files
- Advanced search with filters
- Card sharing via QR codes
- Integration with CRM systems
- Apple Watch companion app
- Siri shortcuts for quick access

## Contributing

This is a complete iOS application ready for development and customization. The modular architecture makes it easy to extend with additional features.

## License

This project is available for educational and development purposes.
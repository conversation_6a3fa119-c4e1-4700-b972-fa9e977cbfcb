import Foundation
import Vision
import UIKit
import Contacts

class BusinessCardScanner: ObservableObject {
    @Published var isScanning = false
    @Published var extractedText = ""
    @Published var parsedData: BusinessCardData?
    
    struct BusinessCardData {
        var name: String?
        var company: String?
        var email: String?
        var phone: String?
        var address: String?
        var website: String?
    }
    
    func scanBusinessCard(from image: UIImage, completion: @escaping (BusinessCardData?) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(nil)
            return
        }
        
        isScanning = true
        
        let request = VNRecognizeTextRequest { [weak self] request, error in
            DispatchQueue.main.async {
                self?.isScanning = false
                
                if let error = error {
                    print("Text recognition error: \(error.localizedDescription)")
                    completion(nil)
                    return
                }
                
                guard let observations = request.results as? [VNRecognizedTextObservation] else {
                    completion(nil)
                    return
                }
                
                let recognizedText = observations.compactMap { observation in
                    observation.topCandidates(1).first?.string
                }.joined(separator: "\n")
                
                self?.extractedText = recognizedText
                let parsedData = self?.parseBusinessCardText(recognizedText)
                self?.parsedData = parsedData
                completion(parsedData)
            }
        }
        
        request.recognitionLevel = .accurate
        request.usesLanguageCorrection = true
        
        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                try handler.perform([request])
            } catch {
                DispatchQueue.main.async {
                    self.isScanning = false
                    print("Failed to perform text recognition: \(error.localizedDescription)")
                    completion(nil)
                }
            }
        }
    }
    
    private func parseBusinessCardText(_ text: String) -> BusinessCardData {
        var data = BusinessCardData()
        let lines = text.components(separatedBy: .newlines).map { $0.trimmingCharacters(in: .whitespaces) }
        
        for line in lines {
            if line.isEmpty { continue }
            
            if data.email == nil && isEmail(line) {
                data.email = line
            } else if data.phone == nil && isPhoneNumber(line) {
                data.phone = formatPhoneNumber(line)
            } else if data.website == nil && isWebsite(line) {
                data.website = line
            } else if data.name == nil && isPossibleName(line) {
                data.name = line
            } else if data.company == nil && isPossibleCompany(line) {
                data.company = line
            } else if data.address == nil && isPossibleAddress(line) {
                data.address = line
            }
        }
        
        return data
    }
    
    private func isEmail(_ text: String) -> Bool {
        let emailRegex = #"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$"#
        return text.range(of: emailRegex, options: .regularExpression) != nil
    }
    
    private func isPhoneNumber(_ text: String) -> Bool {
        let phoneRegex = #"^[\+]?[\s\-\(\)]*([0-9][\s\-\(\)]*){6,}$"#
        return text.range(of: phoneRegex, options: .regularExpression) != nil
    }
    
    private func isWebsite(_ text: String) -> Bool {
        let websiteRegex = #"^(https?://)?(www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$"#
        return text.range(of: websiteRegex, options: .regularExpression) != nil
    }
    
    private func isPossibleName(_ text: String) -> Bool {
        let words = text.components(separatedBy: .whitespaces)
        return words.count >= 2 && words.count <= 4 && 
               words.allSatisfy { word in
                   word.first?.isUppercase == true && 
                   word.allSatisfy { $0.isLetter || $0 == "." || $0 == "'" }
               }
    }
    
    private func isPossibleCompany(_ text: String) -> Bool {
        let companyKeywords = ["Inc", "LLC", "Ltd", "Corp", "Company", "Co", "Corporation", "Incorporated", "Limited"]
        return companyKeywords.contains { keyword in
            text.contains(keyword)
        } || (text.count > 3 && text.count < 50)
    }
    
    private func isPossibleAddress(_ text: String) -> Bool {
        let addressKeywords = ["Street", "St", "Avenue", "Ave", "Road", "Rd", "Boulevard", "Blvd", "Lane", "Ln", "Drive", "Dr"]
        return addressKeywords.contains { keyword in
            text.contains(keyword)
        } || text.contains(try! NSRegularExpression(pattern: #"\d+\s+\w+"#))
    }
    
    private func formatPhoneNumber(_ text: String) -> String {
        let digits = text.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        
        if digits.count == 10 {
            let areaCode = String(digits.prefix(3))
            let middle = String(digits.dropFirst(3).prefix(3))
            let last = String(digits.suffix(4))
            return "(\(areaCode)) \(middle)-\(last)"
        } else if digits.count == 11 && digits.first == "1" {
            let areaCode = String(digits.dropFirst().prefix(3))
            let middle = String(digits.dropFirst(4).prefix(3))
            let last = String(digits.suffix(4))
            return "+1 (\(areaCode)) \(middle)-\(last)"
        }
        
        return text
    }
}
import SwiftUI

struct BusinessCardDetailView: View {
    @ObservedObject var card: BusinessCard
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) var presentationMode
    @State private var showingEditView = false
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                header
                
                if !card.wrappedCompany.isEmpty {
                    infoSection(title: "Company", content: card.wrappedCompany, icon: "building.2")
                }
                
                contactSection
                
                if !card.wrappedAddress.isEmpty {
                    infoSection(title: "Address", content: card.wrappedAddress, icon: "location")
                }
                
                if !card.wrappedWebsite.isEmpty {
                    infoSection(title: "Website", content: card.wrappedWebsite, icon: "globe")
                }
                
                if !card.wrappedNotes.isEmpty {
                    infoSection(title: "Notes", content: card.wrappedNotes, icon: "note.text")
                }
                
                metadataSection
            }
            .padding()
        }
        .navigationTitle(card.wrappedName)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Edit") {
                    showingEditView = true
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            EditBusinessCardView(card: card)
        }
    }
    
    private var header: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(card.wrappedName)
                .font(.largeTitle)
                .fontWeight(.bold)
            
            if let category = card.category {
                HStack {
                    Circle()
                        .fill(category.categoryColor)
                        .frame(width: 12, height: 12)
                    Text(category.wrappedName)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private var contactSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            if !card.wrappedEmail.isEmpty {
                contactRow(icon: "envelope", title: "Email", content: card.wrappedEmail, action: {
                    if let url = URL(string: "mailto:\(card.wrappedEmail)") {
                        UIApplication.shared.open(url)
                    }
                })
            }
            
            if !card.wrappedPhone.isEmpty {
                contactRow(icon: "phone", title: "Phone", content: card.wrappedPhone, action: {
                    if let url = URL(string: "tel:\(card.wrappedPhone)") {
                        UIApplication.shared.open(url)
                    }
                })
            }
        }
    }
    
    private func contactRow(icon: String, title: String, content: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                    .frame(width: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(content)
                        .font(.body)
                        .foregroundColor(.primary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func infoSection(title: String, content: String, icon: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                    .frame(width: 20)
                Text(title)
                    .font(.headline)
            }
            
            Text(content)
                .font(.body)
                .padding(.leading, 28)
        }
    }
    
    private var metadataSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Created")
                .font(.caption)
                .foregroundColor(.secondary)
            Text(card.formattedDateCreated)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.top, 20)
    }
}

struct EditBusinessCardView: View {
    @ObservedObject var card: BusinessCard
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) var presentationMode
    
    @State private var name: String
    @State private var company: String
    @State private var email: String
    @State private var phone: String
    @State private var address: String
    @State private var website: String
    @State private var notes: String
    @State private var selectedCategory: Category?
    
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Category.name, ascending: true)],
        animation: .default)
    private var categories: FetchedResults<Category>
    
    init(card: BusinessCard) {
        self.card = card
        self._name = State(initialValue: card.wrappedName)
        self._company = State(initialValue: card.wrappedCompany)
        self._email = State(initialValue: card.wrappedEmail)
        self._phone = State(initialValue: card.wrappedPhone)
        self._address = State(initialValue: card.wrappedAddress)
        self._website = State(initialValue: card.wrappedWebsite)
        self._notes = State(initialValue: card.wrappedNotes)
        self._selectedCategory = State(initialValue: card.category)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Basic Information")) {
                    TextField("Name", text: $name)
                    TextField("Company", text: $company)
                    TextField("Email", text: $email)
                        .keyboardType(.emailAddress)
                    TextField("Phone", text: $phone)
                        .keyboardType(.phonePad)
                }
                
                Section(header: Text("Category")) {
                    Picker("Category", selection: $selectedCategory) {
                        Text("None").tag(nil as Category?)
                        ForEach(categories, id: \.self) { category in
                            HStack {
                                Circle()
                                    .fill(category.categoryColor)
                                    .frame(width: 12, height: 12)
                                Text(category.wrappedName)
                            }
                            .tag(category as Category?)
                        }
                    }
                }
                
                Section(header: Text("Additional Information")) {
                    TextField("Website", text: $website)
                        .keyboardType(.URL)
                    TextField("Address", text: $address)
                    TextField("Notes", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("Edit Card")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveCard()
                    }
                    .disabled(name.isEmpty)
                }
            }
        }
    }
    
    private func saveCard() {
        withAnimation {
            card.name = name
            card.company = company
            card.email = email
            card.phone = phone
            card.address = address
            card.website = website
            card.notes = notes
            card.category = selectedCategory
            
            do {
                try viewContext.save()
                presentationMode.wrappedValue.dismiss()
            } catch {
                let nsError = error as NSError
                print("Failed to save card: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let sampleCard = BusinessCard(context: context)
    sampleCard.name = "John Doe"
    sampleCard.company = "Tech Corp"
    sampleCard.email = "<EMAIL>"
    sampleCard.phone = "******-123-4567"
    
    return NavigationView {
        BusinessCardDetailView(card: sampleCard)
    }
    .environment(\.managedObjectContext, context)
}
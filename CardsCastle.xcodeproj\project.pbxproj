// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A11E1A2A2C8F123456789ABC /* CardsCastleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A292C8F123456789ABC /* CardsCastleApp.swift */; };
		A11E1A2C2C8F123456789ABC /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A2B2C8F123456789ABC /* ContentView.swift */; };
		A11E1A2E2C8F123456789ABC /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A11E1A2D2C8F123456789ABC /* Assets.xcassets */; };
		A11E1A312C8F123456789ABC /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A11E1A302C8F123456789ABC /* Preview Assets.xcassets */; };
		A11E1A332C8F123456789ABC /* CardsCastle.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A322C8F123456789ABC /* CardsCastle.xcdatamodeld */; };
		A11E1A3A2C8F123456789ABC /* BusinessCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A392C8F123456789ABC /* BusinessCard.swift */; };
		A11E1A3C2C8F123456789ABC /* Category.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A3B2C8F123456789ABC /* Category.swift */; };
		A11E1A3E2C8F123456789ABC /* PersistenceController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A3D2C8F123456789ABC /* PersistenceController.swift */; };
		A11E1A402C8F123456789ABC /* BusinessCardDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A3F2C8F123456789ABC /* BusinessCardDetailView.swift */; };
		A11E1A422C8F123456789ABC /* CategoriesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A412C8F123456789ABC /* CategoriesView.swift */; };
		A11E1A442C8F123456789ABC /* ScanBusinessCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A432C8F123456789ABC /* ScanBusinessCardView.swift */; };
		A11E1A462C8F123456789ABC /* ExportView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A452C8F123456789ABC /* ExportView.swift */; };
		A11E1A482C8F123456789ABC /* BusinessCardScanner.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A472C8F123456789ABC /* BusinessCardScanner.swift */; };
		A11E1A4A2C8F123456789ABC /* ContactExportService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A492C8F123456789ABC /* ContactExportService.swift */; };
		A11E1A4C2C8F123456789ABC /* String+Localization.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A4B2C8F123456789ABC /* String+Localization.swift */; };
		A11E1A4E2C8F123456789ABC /* PermissionsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A4D2C8F123456789ABC /* PermissionsManager.swift */; };
		A11E1A502C8F123456789ABC /* PermissionStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A4F2C8F123456789ABC /* PermissionStatusView.swift */; };
		A11E1A522C8F123456789ABC /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A512C8F123456789ABC /* SettingsView.swift */; };
		A11E1A542C8F123456789ABC /* OnboardingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A11E1A532C8F123456789ABC /* OnboardingView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A11E1A262C8F123456789ABC /* CardsCastle.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CardsCastle.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A11E1A292C8F123456789ABC /* CardsCastleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardsCastleApp.swift; sourceTree = "<group>"; };
		A11E1A2B2C8F123456789ABC /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A11E1A2D2C8F123456789ABC /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A11E1A302C8F123456789ABC /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A11E1A322C8F123456789ABC /* CardsCastle.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = CardsCastle.xcdatamodel; sourceTree = "<group>"; };
		A11E1A392C8F123456789ABC /* BusinessCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BusinessCard.swift; sourceTree = "<group>"; };
		A11E1A3B2C8F123456789ABC /* Category.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Category.swift; sourceTree = "<group>"; };
		A11E1A3D2C8F123456789ABC /* PersistenceController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersistenceController.swift; sourceTree = "<group>"; };
		A11E1A3F2C8F123456789ABC /* BusinessCardDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BusinessCardDetailView.swift; sourceTree = "<group>"; };
		A11E1A412C8F123456789ABC /* CategoriesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoriesView.swift; sourceTree = "<group>"; };
		A11E1A432C8F123456789ABC /* ScanBusinessCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScanBusinessCardView.swift; sourceTree = "<group>"; };
		A11E1A452C8F123456789ABC /* ExportView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExportView.swift; sourceTree = "<group>"; };
		A11E1A472C8F123456789ABC /* BusinessCardScanner.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BusinessCardScanner.swift; sourceTree = "<group>"; };
		A11E1A492C8F123456789ABC /* ContactExportService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactExportService.swift; sourceTree = "<group>"; };
		A11E1A4B2C8F123456789ABC /* String+Localization.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "String+Localization.swift"; sourceTree = "<group>"; };
		A11E1A4D2C8F123456789ABC /* PermissionsManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PermissionsManager.swift; sourceTree = "<group>"; };
		A11E1A4F2C8F123456789ABC /* PermissionStatusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PermissionStatusView.swift; sourceTree = "<group>"; };
		A11E1A512C8F123456789ABC /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		A11E1A532C8F123456789ABC /* OnboardingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardingView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A11E1A232C8F123456789ABC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A11E1A1D2C8F123456789ABC = {
			isa = PBXGroup;
			children = (
				A11E1A282C8F123456789ABC /* CardsCastle */,
				A11E1A272C8F123456789ABC /* Products */,
			);
			sourceTree = "<group>";
		};
		A11E1A272C8F123456789ABC /* Products */ = {
			isa = PBXGroup;
			children = (
				A11E1A262C8F123456789ABC /* CardsCastle.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A11E1A282C8F123456789ABC /* CardsCastle */ = {
			isa = PBXGroup;
			children = (
				A11E1A292C8F123456789ABC /* CardsCastleApp.swift */,
				A11E1A2B2C8F123456789ABC /* ContentView.swift */,
				A11E1A552C8F123456789ABC /* Views */,
				A11E1A382C8F123456789ABC /* Models */,
				A11E1A562C8F123456789ABC /* Services */,
				A11E1A572C8F123456789ABC /* Extensions */,
				A11E1A2D2C8F123456789ABC /* Assets.xcassets */,
				A11E1A322C8F123456789ABC /* CardsCastle.xcdatamodeld */,
				A11E1A2F2C8F123456789ABC /* Preview Content */,
			);
			path = CardsCastle;
			sourceTree = "<group>";
		};
		A11E1A2F2C8F123456789ABC /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A11E1A302C8F123456789ABC /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A11E1A382C8F123456789ABC /* Models */ = {
			isa = PBXGroup;
			children = (
				A11E1A392C8F123456789ABC /* BusinessCard.swift */,
				A11E1A3B2C8F123456789ABC /* Category.swift */,
				A11E1A3D2C8F123456789ABC /* PersistenceController.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A11E1A552C8F123456789ABC /* Views */ = {
			isa = PBXGroup;
			children = (
				A11E1A3F2C8F123456789ABC /* BusinessCardDetailView.swift */,
				A11E1A412C8F123456789ABC /* CategoriesView.swift */,
				A11E1A432C8F123456789ABC /* ScanBusinessCardView.swift */,
				A11E1A452C8F123456789ABC /* ExportView.swift */,
				A11E1A4F2C8F123456789ABC /* PermissionStatusView.swift */,
				A11E1A512C8F123456789ABC /* SettingsView.swift */,
				A11E1A532C8F123456789ABC /* OnboardingView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A11E1A562C8F123456789ABC /* Services */ = {
			isa = PBXGroup;
			children = (
				A11E1A472C8F123456789ABC /* BusinessCardScanner.swift */,
				A11E1A492C8F123456789ABC /* ContactExportService.swift */,
				A11E1A4D2C8F123456789ABC /* PermissionsManager.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A11E1A572C8F123456789ABC /* Extensions */ = {
			isa = PBXGroup;
			children = (
				A11E1A4B2C8F123456789ABC /* String+Localization.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A11E1A252C8F123456789ABC /* CardsCastle */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A11E1A362C8F123456789ABC /* Build configuration list for PBXNativeTarget "CardsCastle" */;
			buildPhases = (
				A11E1A222C8F123456789ABC /* Sources */,
				A11E1A232C8F123456789ABC /* Frameworks */,
				A11E1A242C8F123456789ABC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CardsCastle;
			productName = CardsCastle;
			productReference = A11E1A262C8F123456789ABC /* CardsCastle.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A11E1A1E2C8F123456789ABC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A11E1A252C8F123456789ABC = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A11E1A212C8F123456789ABC /* Build configuration list for PBXProject "CardsCastle" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ar,
				fr,
			);
			mainGroup = A11E1A1D2C8F123456789ABC;
			productRefGroup = A11E1A272C8F123456789ABC /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A11E1A252C8F123456789ABC /* CardsCastle */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A11E1A242C8F123456789ABC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A11E1A312C8F123456789ABC /* Preview Assets.xcassets in Resources */,
				A11E1A2E2C8F123456789ABC /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A11E1A222C8F123456789ABC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A11E1A332C8F123456789ABC /* CardsCastle.xcdatamodeld in Sources */,
				A11E1A3E2C8F123456789ABC /* PersistenceController.swift in Sources */,
				A11E1A2C2C8F123456789ABC /* ContentView.swift in Sources */,
				A11E1A3A2C8F123456789ABC /* BusinessCard.swift in Sources */,
				A11E1A3C2C8F123456789ABC /* Category.swift in Sources */,
				A11E1A2A2C8F123456789ABC /* CardsCastleApp.swift in Sources */,
				A11E1A402C8F123456789ABC /* BusinessCardDetailView.swift in Sources */,
				A11E1A422C8F123456789ABC /* CategoriesView.swift in Sources */,
				A11E1A442C8F123456789ABC /* ScanBusinessCardView.swift in Sources */,
				A11E1A462C8F123456789ABC /* ExportView.swift in Sources */,
				A11E1A482C8F123456789ABC /* BusinessCardScanner.swift in Sources */,
				A11E1A4A2C8F123456789ABC /* ContactExportService.swift in Sources */,
				A11E1A4C2C8F123456789ABC /* String+Localization.swift in Sources */,
				A11E1A4E2C8F123456789ABC /* PermissionsManager.swift in Sources */,
				A11E1A502C8F123456789ABC /* PermissionStatusView.swift in Sources */,
				A11E1A522C8F123456789ABC /* SettingsView.swift in Sources */,
				A11E1A542C8F123456789ABC /* OnboardingView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A11E1A342C8F123456789ABC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A11E1A352C8F123456789ABC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A11E1A372C8F123456789ABC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"CardsCastle/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.cardscastle.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A11E1A382C8F123456789ABC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"CardsCastle/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.cardscastle.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A11E1A212C8F123456789ABC /* Build configuration list for PBXProject "CardsCastle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A11E1A342C8F123456789ABC /* Debug */,
				A11E1A352C8F123456789ABC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A11E1A362C8F123456789ABC /* Build configuration list for PBXNativeTarget "CardsCastle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A11E1A372C8F123456789ABC /* Debug */,
				A11E1A382C8F123456789ABC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		A11E1A322C8F123456789ABC /* CardsCastle.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				A11E1A332C8F123456789ABC /* CardsCastle.xcdatamodel */,
			);
			currentVersion = A11E1A332C8F123456789ABC /* CardsCastle.xcdatamodel */;
			path = CardsCastle.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = A11E1A1E2C8F123456789ABC /* Project object */;
}
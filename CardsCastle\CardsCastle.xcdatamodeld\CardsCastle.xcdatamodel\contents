<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22758" systemVersion="23F79" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="YES" userDefinedModelVersionIdentifier="">
    <entity name="BusinessCard" representedClassName="BusinessCard" syncable="YES" codeGenerationType="class">
        <attribute name="address" optional="YES" attributeType="String"/>
        <attribute name="company" optional="YES" attributeType="String"/>
        <attribute name="dateCreated" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="dateModified" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="email" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="phone" optional="YES" attributeType="String"/>
        <attribute name="website" optional="YES" attributeType="String"/>
        <relationship name="category" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Category" inverseName="businessCards" inverseEntity="Category"/>
    </entity>
    <entity name="Category" representedClassName="Category" syncable="YES" codeGenerationType="class">
        <attribute name="color" optional="YES" attributeType="String"/>
        <attribute name="dateCreated" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <relationship name="businessCards" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="BusinessCard" inverseName="category" inverseEntity="BusinessCard"/>
    </entity>
</model>
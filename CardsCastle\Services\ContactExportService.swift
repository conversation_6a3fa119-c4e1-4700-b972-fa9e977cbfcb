import Foundation
import Contacts
import UIKit

class ContactExportService: ObservableObject {
    @Published var isExporting = false
    @Published var exportProgress: Double = 0.0
    
    func exportToContacts(businessCards: [BusinessCard]) async -> Result<Int, ContactExportError> {
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                self.isExporting = true
                self.exportProgress = 0.0
            }
            
            let store = CNContactStore()
            
            // Request permission
            store.requestAccess(for: .contacts) { granted, error in
                if !granted {
                    DispatchQueue.main.async {
                        self.isExporting = false
                    }
                    continuation.resume(returning: .failure(.permissionDenied))
                    return
                }
                
                var successCount = 0
                let totalCards = businessCards.count
                
                for (index, card) in businessCards.enumerated() {
                    let contact = CNMutableContact()
                    
                    // Parse name
                    if let name = card.name {
                        let nameComponents = name.components(separatedBy: " ")
                        if nameComponents.count >= 1 {
                            contact.givenName = nameComponents.first ?? ""
                        }
                        if nameComponents.count >= 2 {
                            contact.familyName = nameComponents.dropFirst().joined(separator: " ")
                        }
                    }
                    
                    // Set organization
                    if let company = card.company {
                        contact.organizationName = company
                    }
                    
                    // Set email
                    if let email = card.email, !email.isEmpty {
                        let emailAddress = CNLabeledValue(label: CNLabelWork, value: email as NSString)
                        contact.emailAddresses = [emailAddress]
                    }
                    
                    // Set phone
                    if let phone = card.phone, !phone.isEmpty {
                        let phoneNumber = CNPhoneNumber(stringValue: phone)
                        let phoneValue = CNLabeledValue(label: CNLabelWork, value: phoneNumber)
                        contact.phoneNumbers = [phoneValue]
                    }
                    
                    // Set address
                    if let address = card.address, !address.isEmpty {
                        let postalAddress = CNMutablePostalAddress()
                        postalAddress.street = address
                        let addressValue = CNLabeledValue(label: CNLabelWork, value: postalAddress)
                        contact.postalAddresses = [addressValue]
                    }
                    
                    // Set website
                    if let website = card.website, !website.isEmpty {
                        let urlValue = CNLabeledValue(label: CNLabelWork, value: website as NSString)
                        contact.urlAddresses = [urlValue]
                    }
                    
                    // Set notes
                    if let notes = card.notes, !notes.isEmpty {
                        contact.note = notes
                    }
                    
                    // Save contact
                    let saveRequest = CNSaveRequest()
                    saveRequest.add(contact, toContainerWithIdentifier: nil)
                    
                    do {
                        try store.execute(saveRequest)
                        successCount += 1
                    } catch {
                        print("Failed to save contact for \(card.name ?? "Unknown"): \(error)")
                    }
                    
                    // Update progress
                    DispatchQueue.main.async {
                        self.exportProgress = Double(index + 1) / Double(totalCards)
                    }
                }
                
                DispatchQueue.main.async {
                    self.isExporting = false
                    self.exportProgress = 0.0
                }
                
                continuation.resume(returning: .success(successCount))
            }
        }
    }
    
    func exportToVCard(businessCards: [BusinessCard]) -> String {
        var vcardString = ""
        
        for card in businessCards {
            vcardString += "BEGIN:VCARD\n"
            vcardString += "VERSION:3.0\n"
            
            if let name = card.name {
                let nameComponents = name.components(separatedBy: " ")
                let firstName = nameComponents.first ?? ""
                let lastName = nameComponents.dropFirst().joined(separator: " ")
                vcardString += "FN:\(name)\n"
                vcardString += "N:\(lastName);\(firstName);;;\n"
            }
            
            if let company = card.company {
                vcardString += "ORG:\(company)\n"
            }
            
            if let email = card.email {
                vcardString += "EMAIL;TYPE=WORK:\(email)\n"
            }
            
            if let phone = card.phone {
                vcardString += "TEL;TYPE=WORK:\(phone)\n"
            }
            
            if let address = card.address {
                vcardString += "ADR;TYPE=WORK:;;\(address);;;;\n"
            }
            
            if let website = card.website {
                vcardString += "URL;TYPE=WORK:\(website)\n"
            }
            
            if let notes = card.notes {
                vcardString += "NOTE:\(notes)\n"
            }
            
            vcardString += "END:VCARD\n\n"
        }
        
        return vcardString
    }
    
    func exportToCSV(businessCards: [BusinessCard]) -> String {
        var csvString = "Name,Company,Email,Phone,Address,Website,Notes,Category,Date Created\n"
        
        for card in businessCards {
            let name = csvEscape(card.name ?? "")
            let company = csvEscape(card.company ?? "")
            let email = csvEscape(card.email ?? "")
            let phone = csvEscape(card.phone ?? "")
            let address = csvEscape(card.address ?? "")
            let website = csvEscape(card.website ?? "")
            let notes = csvEscape(card.notes ?? "")
            let category = csvEscape(card.category?.name ?? "")
            let dateCreated = card.dateCreated?.formatted(date: .abbreviated, time: .omitted) ?? ""
            
            csvString += "\(name),\(company),\(email),\(phone),\(address),\(website),\(notes),\(category),\(dateCreated)\n"
        }
        
        return csvString
    }
    
    private func csvEscape(_ text: String) -> String {
        let escaped = text.replacingOccurrences(of: "\"", with: "\"\"")
        if escaped.contains(",") || escaped.contains("\"") || escaped.contains("\n") {
            return "\"\(escaped)\""
        }
        return escaped
    }
}

enum ContactExportError: LocalizedError {
    case permissionDenied
    case exportFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Permission to access contacts was denied"
        case .exportFailed(let message):
            return "Export failed: \(message)"
        }
    }
}
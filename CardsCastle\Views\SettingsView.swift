import SwiftUI
import AVFoundation
import Photos
import Contacts

struct SettingsView: View {
    @StateObject private var permissionsManager = PermissionsManager()
    @State private var showingPermissionsView = false
    @State private var showingAboutView = false
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("Permissions")) {
                    Button(action: {
                        showingPermissionsView = true
                    }) {
                        HStack {
                            Image(systemName: "lock.shield")
                                .foregroundColor(.blue)
                                .frame(width: 30)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Privacy & Permissions")
                                    .foregroundColor(.primary)
                                
                                Text(permissionsSummary)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Section(header: Text("Data")) {
                    Button(action: {
                        requestAllPermissions()
                    }) {
                        HStack {
                            Image(systemName: "checkmark.shield")
                                .foregroundColor(.green)
                                .frame(width: 30)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Enable All Permissions")
                                    .foregroundColor(.primary)
                                
                                Text("Grant camera, photo library, and contacts access")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    
                    NavigationLink(destination: DataManagementView()) {
                        HStack {
                            Image(systemName: "externaldrive")
                                .foregroundColor(.blue)
                                .frame(width: 30)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Data Management")
                                    .foregroundColor(.primary)
                                
                                Text("Backup, export, and sync options")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                Section(header: Text("About")) {
                    Button(action: {
                        showingAboutView = true
                    }) {
                        HStack {
                            Image(systemName: "info.circle")
                                .foregroundColor(.blue)
                                .frame(width: 30)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("About CardsCastle")
                                    .foregroundColor(.primary)
                                
                                Text("Version 1.0")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .navigationTitle("Settings")
            .onAppear {
                updatePermissionStatuses()
            }
            .sheet(isPresented: $showingPermissionsView) {
                PermissionStatusView()
            }
            .sheet(isPresented: $showingAboutView) {
                AboutView()
            }
            .permissionAlerts(permissionsManager)
        }
    }
    
    private var permissionsSummary: String {
        var granted = 0
        var total = 3
        
        if permissionsManager.cameraStatus == .authorized {
            granted += 1
        }
        if permissionsManager.photoLibraryStatus == .authorized || permissionsManager.photoLibraryStatus == .limited {
            granted += 1
        }
        if permissionsManager.contactsStatus == .authorized {
            granted += 1
        }
        
        return "\(granted) of \(total) permissions granted"
    }
    
    private func updatePermissionStatuses() {
        permissionsManager.cameraStatus = AVCaptureDevice.authorizationStatus(for: .video)
        permissionsManager.photoLibraryStatus = PHPhotoLibrary.authorizationStatus()
        permissionsManager.contactsStatus = CNContactStore.authorizationStatus(for: .contacts)
    }
    
    private func requestAllPermissions() {
        permissionsManager.requestCameraPermission { _ in
            permissionsManager.requestPhotoLibraryPermission { _ in
                permissionsManager.requestContactsPermission { _ in
                    updatePermissionStatuses()
                }
            }
        }
    }
}

struct DataManagementView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \BusinessCard.name, ascending: true)],
        animation: .default)
    private var businessCards: FetchedResults<BusinessCard>
    
    @State private var showingExportView = false
    
    var body: some View {
        List {
            Section(header: Text("Export Data")) {
                Button(action: {
                    showingExportView = true
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.blue)
                            .frame(width: 30)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Export All Cards")
                                .foregroundColor(.primary)
                            
                            Text("\(businessCards.count) business cards")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .disabled(businessCards.isEmpty)
            }
            
            Section(header: Text("iCloud Sync"), footer: Text("Your business cards are automatically synced across all your devices signed in with the same Apple ID when iCloud is enabled.")) {
                HStack {
                    Image(systemName: "icloud")
                        .foregroundColor(.blue)
                        .frame(width: 30)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("CloudKit Sync")
                        Text("Automatic synchronization enabled")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                }
            }
            
            Section(header: Text("Storage Info")) {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Business Cards:")
                        Spacer()
                        Text("\(businessCards.count)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Categories:")
                        Spacer()
                        Text("\(categoryCount)")
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .navigationTitle("Data Management")
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $showingExportView) {
            ExportView(businessCards: Array(businessCards))
        }
    }
    
    private var categoryCount: Int {
        let request: NSFetchRequest<Category> = Category.fetchRequest()
        return (try? viewContext.count(for: request)) ?? 0
    }
}

struct AboutView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    VStack(spacing: 16) {
                        Image(systemName: "person.crop.rectangle.stack.fill")
                            .font(.system(size: 80))
                            .foregroundColor(.blue)
                        
                        Text("CardsCastle")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Business Card Manager")
                            .font(.title2)
                            .foregroundColor(.secondary)
                        
                        Text("Version 1.0")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 20)
                    
                    VStack(alignment: .leading, spacing: 16) {
                        FeatureRow(icon: "viewfinder", title: "OCR Scanning", description: "Scan business cards with camera and automatically extract contact information")
                        
                        FeatureRow(icon: "folder", title: "Smart Categories", description: "Organize your contacts with custom categories and color coding")
                        
                        FeatureRow(icon: "magnifyingglass", title: "Powerful Search", description: "Find contacts quickly with intelligent search and filtering")
                        
                        FeatureRow(icon: "square.and.arrow.up", title: "Export Options", description: "Export to vCard, CSV, or iOS Contacts with flexible format options")
                        
                        FeatureRow(icon: "icloud", title: "iCloud Sync", description: "Automatic synchronization across all your Apple devices")
                        
                        FeatureRow(icon: "globe", title: "Multilingual", description: "Full support for English, Arabic, and French languages")
                    }
                    .padding()
                    
                    VStack(spacing: 8) {
                        Text("Privacy First")
                            .font(.headline)
                        
                        Text("CardsCastle keeps your data private and secure. All information is stored locally on your device and optionally synced through your personal iCloud account. No third-party servers or analytics.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                }
            }
            .navigationTitle("About")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)
                .padding(.top, 2)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    SettingsView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
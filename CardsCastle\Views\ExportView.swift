import SwiftUI
import UniformTypeIdentifiers

struct ExportView: View {
    let businessCards: [BusinessCard]
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var exportService = ContactExportService()
    @StateObject private var permissionsManager = PermissionsManager()
    
    @State private var selectedFormat: ExportFormat = .vcard
    @State private var showingShareSheet = false
    @State private var exportedContent = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isExportingToContacts = false
    
    enum ExportFormat: String, CaseIterable {
        case vcard = "vCard"
        case csv = "CSV"
        case contacts = "iOS Contacts"
        
        var fileExtension: String {
            switch self {
            case .vcard: return "vcf"
            case .csv: return "csv"
            case .contacts: return ""
            }
        }
        
        var icon: String {
            switch self {
            case .vcard: return "person.crop.rectangle"
            case .csv: return "tablecells"
            case .contacts: return "person.crop.circle.badge.plus"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Export \(businessCards.count) business card\(businessCards.count == 1 ? "" : "s")")
                    .font(.headline)
                    .padding(.top)
                
                VStack(spacing: 16) {
                    ForEach(ExportFormat.allCases, id: \.self) { format in
                        ExportFormatRow(
                            format: format,
                            isSelected: selectedFormat == format
                        ) {
                            selectedFormat = format
                        }
                    }
                }
                .padding()
                
                if exportService.isExporting {
                    VStack(spacing: 12) {
                        ProgressView(value: exportService.exportProgress)
                            .progressViewStyle(LinearProgressViewStyle())
                        
                        Text("Exporting to Contacts...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
                
                Spacer()
                
                VStack(spacing: 12) {
                    Button(action: performExport) {
                        HStack {
                            Image(systemName: selectedFormat.icon)
                            Text("Export as \(selectedFormat.rawValue)")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(exportService.isExporting)
                    
                    if selectedFormat != .contacts {
                        Text("File will be shared using iOS Share Sheet")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
            }
            .navigationTitle("Export Business Cards")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingShareSheet) {
                if selectedFormat != .contacts {
                    ShareSheet(
                        activityItems: [createExportFile()],
                        applicationActivities: nil
                    )
                }
            }
            .alert("Export Result", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
            .permissionAlerts(permissionsManager)
        }
    }
    
    private func exportToContacts() {
        permissionsManager.requestContactsPermission { granted in
            if granted {
                Task {
                    let result = await exportService.exportToContacts(businessCards: businessCards)
                    
                    DispatchQueue.main.async {
                        switch result {
                        case .success(let count):
                            alertMessage = "Successfully exported \(count) of \(businessCards.count) contacts to iOS Contacts app."
                            showingAlert = true
                        case .failure(let error):
                            alertMessage = error.localizedDescription
                            showingAlert = true
                        }
                    }
                }
            }
        }
    }
    
    private func performExport() {
        switch selectedFormat {
        case .vcard:
            exportedContent = exportService.exportToVCard(businessCards: businessCards)
            showingShareSheet = true
            
        case .csv:
            exportedContent = exportService.exportToCSV(businessCards: businessCards)
            showingShareSheet = true
            
        case .contacts:
            exportToContacts()
        }
    }
    
    private func createExportFile() -> URL {
        let fileName = "business_cards_export.\(selectedFormat.fileExtension)"
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        do {
            try exportedContent.write(to: tempURL, atomically: true, encoding: .utf8)
        } catch {
            print("Failed to write export file: \(error)")
        }
        
        return tempURL
    }
}

struct ExportFormatRow: View {
    let format: ExportView.ExportFormat
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                Image(systemName: format.icon)
                    .frame(width: 30)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(format.rawValue)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(formatDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(isSelected ? Color.blue : Color.secondary.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var formatDescription: String {
        switch format {
        case .vcard:
            return "Standard contact format compatible with most applications"
        case .csv:
            return "Spreadsheet format for data analysis and bulk operations"
        case .contacts:
            return "Export directly to iOS Contacts app on this device"
        }
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    let applicationActivities: [UIActivity]?
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: applicationActivities
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let sampleCards = [
        {
            let card = BusinessCard(context: context)
            card.name = "John Doe"
            card.company = "Tech Corp"
            card.email = "<EMAIL>"
            return card
        }(),
        {
            let card = BusinessCard(context: context)
            card.name = "Jane Smith"
            card.company = "Design Studio"
            card.email = "<EMAIL>"
            return card
        }()
    ]
    
    return ExportView(businessCards: sampleCards)
}
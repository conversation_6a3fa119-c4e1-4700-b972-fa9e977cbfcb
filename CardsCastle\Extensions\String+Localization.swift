import Foundation

extension String {
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    func localized(with arguments: CVarArg...) -> String {
        return String(format: NSLocalizedString(self, comment: ""), arguments: arguments)
    }
}

struct LocalizedString {
    static let cards = "cards".localized
    static let categories = "categories".localized
    static let searchPlaceholder = "search_placeholder".localized
    static let cancel = "cancel".localized
    static let save = "save".localized
    static let edit = "edit".localized
    static let delete = "delete".localized
    static let add = "add".localized
    static let clear = "clear".localized
    static let all = "all".localized
    static let none = "none".localized
    
    static let businessCards = "business_cards".localized
    static let addCard = "add_card".localized
    static let addBusinessCard = "add_business_card".localized
    static let editCard = "edit_card".localized
    
    static let name = "name".localized
    static let company = "company".localized
    static let email = "email".localized
    static let phone = "phone".localized
    static let address = "address".localized
    static let website = "website".localized
    static let notes = "notes".localized
    static let category = "category".localized
    static let created = "created".localized
    
    static let addCategory = "add_category".localized
    static let categoryName = "category_name".localized
    static let color = "color".localized
    static let filterByCategory = "filter_by_category".localized
    static let allCards = "all_cards".localized
    
    static let basicInformation = "basic_information".localized
    static let additionalInformation = "additional_information".localized
    static let categoryInformation = "category_information".localized
    
    static let clients = "clients".localized
    static let vendors = "vendors".localized
    static let networkingContacts = "networking_contacts".localized
    static let prospects = "prospects".localized
    
    static let unknown = "unknown".localized
    static let uncategorized = "uncategorized".localized
    
    static func cardCount(_ count: Int) -> String {
        if count == 1 {
            return "\(count) \("card_count_singular".localized)"
        } else {
            return "\(count) \("card_count_plural".localized)"
        }
    }
}
import SwiftUI
import AVFoundation
import Photos
import Contacts

struct OnboardingView: View {
    @StateObject private var permissionsManager = PermissionsManager()
    @Binding var isShowing: Bool
    
    @State private var currentPage = 0
    private let totalPages = 4
    
    var body: some View {
        VStack(spacing: 0) {
            // Progress indicator
            HStack {
                ForEach(0..<totalPages, id: \.self) { index in
                    Circle()
                        .fill(index <= currentPage ? Color.blue : Color.gray.opacity(0.3))
                        .frame(width: 8, height: 8)
                        .animation(.easeInOut, value: currentPage)
                }
            }
            .padding(.top, 50)
            .padding(.bottom, 30)
            
            TabView(selection: $currentPage) {
                // Welcome Page
                OnboardingPageView(
                    icon: "person.crop.rectangle.stack.fill",
                    title: "Welcome to CardsCastle",
                    description: "Your digital business card organizer with smart scanning, categorization, and cloud sync.",
                    buttonTitle: "Get Started",
                    buttonAction: {
                        withAnimation {
                            currentPage = 1
                        }
                    }
                )
                .tag(0)
                
                // Camera Permission Page
                PermissionPageView(
                    icon: "camera",
                    title: "Camera Access",
                    description: "CardsCastle uses your camera to scan business cards and automatically extract contact information using advanced OCR technology.",
                    buttonTitle: permissionsManager.cameraStatus == .authorized ? "Camera Enabled ✓" : "Enable Camera",
                    buttonAction: {
                        if permissionsManager.cameraStatus != .authorized {
                            permissionsManager.requestCameraPermission { _ in
                                withAnimation {
                                    currentPage = 2
                                }
                            }
                        } else {
                            withAnimation {
                                currentPage = 2
                            }
                        }
                    },
                    skipAction: {
                        withAnimation {
                            currentPage = 2
                        }
                    },
                    isGranted: permissionsManager.cameraStatus == .authorized
                )
                .tag(1)
                
                // Photo Library Permission Page
                PermissionPageView(
                    icon: "photo",
                    title: "Photo Library Access",
                    description: "Access your photo library to select business card images for scanning when you don't want to take a new photo.",
                    buttonTitle: (permissionsManager.photoLibraryStatus == .authorized || permissionsManager.photoLibraryStatus == .limited) ? "Photos Enabled ✓" : "Enable Photos",
                    buttonAction: {
                        if permissionsManager.photoLibraryStatus == .notDetermined {
                            permissionsManager.requestPhotoLibraryPermission { _ in
                                withAnimation {
                                    currentPage = 3
                                }
                            }
                        } else {
                            withAnimation {
                                currentPage = 3
                            }
                        }
                    },
                    skipAction: {
                        withAnimation {
                            currentPage = 3
                        }
                    },
                    isGranted: permissionsManager.photoLibraryStatus == .authorized || permissionsManager.photoLibraryStatus == .limited
                )
                .tag(2)
                
                // Contacts Permission Page
                PermissionPageView(
                    icon: "person.crop.circle.badge.plus",
                    title: "Contacts Access",
                    description: "Export your business cards directly to the iOS Contacts app for easy integration with your existing contacts.",
                    buttonTitle: permissionsManager.contactsStatus == .authorized ? "Contacts Enabled ✓" : "Enable Contacts",
                    buttonAction: {
                        if permissionsManager.contactsStatus != .authorized {
                            permissionsManager.requestContactsPermission { _ in
                                completeOnboarding()
                            }
                        } else {
                            completeOnboarding()
                        }
                    },
                    skipAction: {
                        completeOnboarding()
                    },
                    isGranted: permissionsManager.contactsStatus == .authorized,
                    isLastPage: true
                )
                .tag(3)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .animation(.easeInOut, value: currentPage)
            
            Spacer()
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.blue.opacity(0.1), Color.clear]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .permissionAlerts(permissionsManager)
        .onAppear {
            updatePermissionStatuses()
        }
    }
    
    private func updatePermissionStatuses() {
        permissionsManager.cameraStatus = AVCaptureDevice.authorizationStatus(for: .video)
        permissionsManager.photoLibraryStatus = PHPhotoLibrary.authorizationStatus()
        permissionsManager.contactsStatus = CNContactStore.authorizationStatus(for: .contacts)
    }
    
    private func completeOnboarding() {
        UserDefaults.standard.set(true, forKey: "HasCompletedOnboarding")
        withAnimation(.easeInOut(duration: 0.5)) {
            isShowing = false
        }
    }
}

struct OnboardingPageView: View {
    let icon: String
    let title: String
    let description: String
    let buttonTitle: String
    let buttonAction: () -> Void
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            Image(systemName: icon)
                .font(.system(size: 100))
                .foregroundColor(.blue)
            
            VStack(spacing: 16) {
                Text(title)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            Spacer()
            
            Button(action: buttonAction) {
                Text(buttonTitle)
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
            }
            .padding(.horizontal, 40)
            .padding(.bottom, 50)
        }
    }
}

struct PermissionPageView: View {
    let icon: String
    let title: String
    let description: String
    let buttonTitle: String
    let buttonAction: () -> Void
    let skipAction: () -> Void
    let isGranted: Bool
    var isLastPage: Bool = false
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            ZStack {
                Circle()
                    .fill(isGranted ? Color.green.opacity(0.2) : Color.blue.opacity(0.2))
                    .frame(width: 120, height: 120)
                
                Image(systemName: icon)
                    .font(.system(size: 50))
                    .foregroundColor(isGranted ? .green : .blue)
                
                if isGranted {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(.green)
                                .background(Color.white)
                                .clipShape(Circle())
                        }
                    }
                    .frame(width: 120, height: 120)
                }
            }
            
            VStack(spacing: 16) {
                Text(title)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            Spacer()
            
            VStack(spacing: 12) {
                Button(action: buttonAction) {
                    Text(buttonTitle)
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(isGranted ? Color.green : Color.blue)
                        .cornerRadius(12)
                }
                .disabled(isGranted && !isLastPage)
                
                if !isLastPage {
                    Button(action: skipAction) {
                        Text("Skip for Now")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 40)
            .padding(.bottom, 50)
        }
    }
}

#Preview {
    OnboardingView(isShowing: .constant(true))
}
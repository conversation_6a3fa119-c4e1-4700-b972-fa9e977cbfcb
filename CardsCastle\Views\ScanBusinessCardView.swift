import SwiftUI
import UIKit

struct ScanBusinessCardView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var scanner = BusinessCardScanner()
    @StateObject private var permissionsManager = PermissionsManager()
    
    @State private var showingImagePicker = false
    @State private var showingCamera = false
    @State private var selectedImage: UIImage?
    @State private var showingConfirmation = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if scanner.isScanning {
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.5)
                        Text("Scanning business card...")
                            .font(.headline)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if selectedImage != nil {
                    scanResultView
                } else {
                    scanOptionsView
                }
            }
            .padding()
            .navigationTitle("Scan Business Card")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingImagePicker) {
                ImagePicker(image: $selectedImage, sourceType: .photoLibrary) { image in
                    if let image = image {
                        processImage(image)
                    }
                }
            }
            .sheet(isPresented: $showingCamera) {
                ImagePicker(image: $selectedImage, sourceType: .camera) { image in
                    if let image = image {
                        processImage(image)
                    }
                }
            }
            .sheet(isPresented: $showingConfirmation) {
                if let parsedData = scanner.parsedData {
                    ConfirmScannedCardView(parsedData: parsedData) {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .permissionAlerts(permissionsManager)
        }
    }
    
    private func requestCameraAccess() {
        permissionsManager.requestCameraPermission { granted in
            if granted {
                showingCamera = true
            }
        }
    }
    
    private func requestPhotoLibraryAccess() {
        permissionsManager.requestPhotoLibraryPermission { granted in
            if granted {
                showingImagePicker = true
            }
        }
    }
    
    private var scanOptionsView: some View {
        VStack(spacing: 30) {
            Image(systemName: "viewfinder")
                .font(.system(size: 80))
                .foregroundColor(.blue)
            
            Text("Scan a business card to automatically extract contact information")
                .font(.title2)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            VStack(spacing: 16) {
                Button(action: {
                    requestCameraAccess()
                }) {
                    HStack {
                        Image(systemName: "camera")
                        Text("Take Photo")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                
                Button(action: {
                    requestPhotoLibraryAccess()
                }) {
                    HStack {
                        Image(systemName: "photo")
                        Text("Choose from Library")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.secondary.opacity(0.1))
                    .foregroundColor(.primary)
                    .cornerRadius(10)
                }
            }
        }
    }
    
    private var scanResultView: some View {
        VStack(spacing: 20) {
            if let image = selectedImage {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(height: 200)
                    .cornerRadius(10)
                    .shadow(radius: 5)
            }
            
            if let parsedData = scanner.parsedData {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Extracted Information:")
                        .font(.headline)
                    
                    if let name = parsedData.name {
                        InfoRow(label: "Name", value: name)
                    }
                    if let company = parsedData.company {
                        InfoRow(label: "Company", value: company)
                    }
                    if let email = parsedData.email {
                        InfoRow(label: "Email", value: email)
                    }
                    if let phone = parsedData.phone {
                        InfoRow(label: "Phone", value: phone)
                    }
                    if let website = parsedData.website {
                        InfoRow(label: "Website", value: website)
                    }
                    if let address = parsedData.address {
                        InfoRow(label: "Address", value: address)
                    }
                }
                .padding()
                .background(Color.secondary.opacity(0.1))
                .cornerRadius(10)
                
                Button("Create Business Card") {
                    showingConfirmation = true
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            
            Button("Scan Another") {
                selectedImage = nil
                scanner.parsedData = nil
                scanner.extractedText = ""
            }
            .foregroundColor(.blue)
        }
    }
    
    private func processImage(_ image: UIImage) {
        scanner.scanBusinessCard(from: image) { _ in
            // The scanner will update its published properties
        }
    }
}

struct InfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label + ":")
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 60, alignment: .leading)
            Text(value)
                .font(.body)
            Spacer()
        }
    }
}

struct ConfirmScannedCardView: View {
    let parsedData: BusinessCardScanner.BusinessCardData
    let onSave: () -> Void
    
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) var presentationMode
    
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Category.name, ascending: true)],
        animation: .default)
    private var categories: FetchedResults<Category>
    
    @State private var name: String
    @State private var company: String
    @State private var email: String
    @State private var phone: String
    @State private var address: String
    @State private var website: String
    @State private var notes = ""
    @State private var selectedCategory: Category?
    
    init(parsedData: BusinessCardScanner.BusinessCardData, onSave: @escaping () -> Void) {
        self.parsedData = parsedData
        self.onSave = onSave
        self._name = State(initialValue: parsedData.name ?? "")
        self._company = State(initialValue: parsedData.company ?? "")
        self._email = State(initialValue: parsedData.email ?? "")
        self._phone = State(initialValue: parsedData.phone ?? "")
        self._address = State(initialValue: parsedData.address ?? "")
        self._website = State(initialValue: parsedData.website ?? "")
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Basic Information")) {
                    TextField("Name", text: $name)
                    TextField("Company", text: $company)
                    TextField("Email", text: $email)
                        .keyboardType(.emailAddress)
                    TextField("Phone", text: $phone)
                        .keyboardType(.phonePad)
                }
                
                Section(header: Text("Category")) {
                    Picker("Category", selection: $selectedCategory) {
                        Text("None").tag(nil as Category?)
                        ForEach(categories, id: \.self) { category in
                            HStack {
                                Circle()
                                    .fill(category.categoryColor)
                                    .frame(width: 12, height: 12)
                                Text(category.wrappedName)
                            }
                            .tag(category as Category?)
                        }
                    }
                }
                
                Section(header: Text("Additional Information")) {
                    TextField("Website", text: $website)
                        .keyboardType(.URL)
                    TextField("Address", text: $address)
                    TextField("Notes", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("Confirm Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveCard()
                    }
                    .disabled(name.isEmpty)
                }
            }
        }
    }
    
    private func saveCard() {
        withAnimation {
            let newCard = BusinessCard(context: viewContext)
            newCard.name = name
            newCard.company = company
            newCard.email = email
            newCard.phone = phone
            newCard.address = address
            newCard.website = website
            newCard.notes = notes
            newCard.category = selectedCategory
            
            do {
                try viewContext.save()
                presentationMode.wrappedValue.dismiss()
                onSave()
            } catch {
                let nsError = error as NSError
                print("Failed to save scanned card: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

struct ImagePicker: UIViewControllerRepresentable {
    @Binding var image: UIImage?
    let sourceType: UIImagePickerController.SourceType
    let onImagePicked: (UIImage?) -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.image = image
                parent.onImagePicked(image)
            }
            picker.dismiss(animated: true)
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.onImagePicked(nil)
            picker.dismiss(animated: true)
        }
    }
}

#Preview {
    ScanBusinessCardView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
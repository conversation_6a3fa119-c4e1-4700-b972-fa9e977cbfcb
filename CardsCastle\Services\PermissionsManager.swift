import Foundation
import AVFoundation
import Photos
import Contacts
import SwiftUI

class PermissionsManager: ObservableObject {
    @Published var cameraStatus: AVAuthorizationStatus = AVCaptureDevice.authorizationStatus(for: .video)
    @Published var photoLibraryStatus: PHAuthorizationStatus = PHPhotoLibrary.authorizationStatus()
    @Published var contactsStatus: CNAuthorizationStatus = CNContactStore.authorizationStatus(for: .contacts)
    
    // Alert states
    @Published var showingCameraAlert = false
    @Published var showingPhotoLibraryAlert = false
    @Published var showingContactsAlert = false
    @Published var showingSettingsAlert = false
    @Published var settingsAlertMessage = ""
    
    func requestCameraPermission(completion: @escaping (Bool) -> Void) {
        switch cameraStatus {
        case .authorized:
            completion(true)
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                DispatchQueue.main.async {
                    self?.cameraStatus = AVCaptureDevice.authorizationStatus(for: .video)
                    completion(granted)
                }
            }
        case .denied, .restricted:
            DispatchQueue.main.async {
                self.settingsAlertMessage = "Camera access is required to scan business cards. Please enable camera access in Settings."
                self.showingSettingsAlert = true
            }
            completion(false)
        @unknown default:
            completion(false)
        }
    }
    
    func requestPhotoLibraryPermission(completion: @escaping (Bool) -> Void) {
        switch photoLibraryStatus {
        case .authorized, .limited:
            completion(true)
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                DispatchQueue.main.async {
                    self?.photoLibraryStatus = status
                    completion(status == .authorized || status == .limited)
                }
            }
        case .denied, .restricted:
            DispatchQueue.main.async {
                self.settingsAlertMessage = "Photo Library access is required to select business card images for scanning. Please enable photo access in Settings."
                self.showingSettingsAlert = true
            }
            completion(false)
        @unknown default:
            completion(false)
        }
    }
    
    func requestContactsPermission(completion: @escaping (Bool) -> Void) {
        switch contactsStatus {
        case .authorized:
            completion(true)
        case .notDetermined:
            let store = CNContactStore()
            store.requestAccess(for: .contacts) { [weak self] granted, error in
                DispatchQueue.main.async {
                    self?.contactsStatus = CNContactStore.authorizationStatus(for: .contacts)
                    completion(granted)
                }
            }
        case .denied, .restricted:
            DispatchQueue.main.async {
                self.settingsAlertMessage = "Contacts access is required to export your business cards to the iOS Contacts app. Please enable contacts access in Settings."
                self.showingSettingsAlert = true
            }
            completion(false)
        @unknown default:
            completion(false)
        }
    }
    
    func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    // Check if permissions are needed before showing UI
    func checkCameraPermission() -> Bool {
        cameraStatus = AVCaptureDevice.authorizationStatus(for: .video)
        return cameraStatus == .authorized
    }
    
    func checkPhotoLibraryPermission() -> Bool {
        photoLibraryStatus = PHPhotoLibrary.authorizationStatus()
        return photoLibraryStatus == .authorized || photoLibraryStatus == .limited
    }
    
    func checkContactsPermission() -> Bool {
        contactsStatus = CNContactStore.authorizationStatus(for: .contacts)
        return contactsStatus == .authorized
    }
}

// Permission request alerts view modifier
struct PermissionAlertsModifier: ViewModifier {
    @ObservedObject var permissionsManager: PermissionsManager
    
    func body(content: Content) -> some View {
        content
            .alert("Camera Permission Required", isPresented: $permissionsManager.showingCameraAlert) {
                Button("Settings") {
                    permissionsManager.openSettings()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("CardsCastle needs camera access to scan business cards. Please enable camera access in Settings → Privacy & Security → Camera.")
            }
            .alert("Photo Library Permission Required", isPresented: $permissionsManager.showingPhotoLibraryAlert) {
                Button("Settings") {
                    permissionsManager.openSettings()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("CardsCastle needs photo library access to select business card images for scanning. Please enable photo access in Settings → Privacy & Security → Photos.")
            }
            .alert("Contacts Permission Required", isPresented: $permissionsManager.showingContactsAlert) {
                Button("Settings") {
                    permissionsManager.openSettings()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("CardsCastle needs contacts access to export your business cards to the iOS Contacts app. Please enable contacts access in Settings → Privacy & Security → Contacts.")
            }
            .alert("Permission Required", isPresented: $permissionsManager.showingSettingsAlert) {
                Button("Settings") {
                    permissionsManager.openSettings()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text(permissionsManager.settingsAlertMessage)
            }
    }
}

extension View {
    func permissionAlerts(_ permissionsManager: PermissionsManager) -> some View {
        modifier(PermissionAlertsModifier(permissionsManager: permissionsManager))
    }
}
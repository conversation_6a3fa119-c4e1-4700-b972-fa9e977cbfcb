import Foundation
import CoreData
import CloudKit

@objc(BusinessCard)
public class BusinessCard: NSManagedObject {
    
}

extension BusinessCard {
    @nonobjc public class func fetchRequest() -> NSFetchRequest<BusinessCard> {
        return NSFetchRequest<BusinessCard>(entityName: "BusinessCard")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var company: String?
    @NSManaged public var email: String?
    @NSManaged public var phone: String?
    @NSManaged public var address: String?
    @NSManaged public var website: String?
    @NSManaged public var notes: String?
    @NSManaged public var dateCreated: Date?
    @NSManaged public var dateModified: Date?
    @NSManaged public var category: Category?
}

extension BusinessCard : Identifiable {
    public var wrappedName: String {
        name ?? "Unknown"
    }
    
    public var wrappedCompany: String {
        company ?? ""
    }
    
    public var wrappedEmail: String {
        email ?? ""
    }
    
    public var wrappedPhone: String {
        phone ?? ""
    }
    
    public var wrappedAddress: String {
        address ?? ""
    }
    
    public var wrappedWebsite: String {
        website ?? ""
    }
    
    public var wrappedNotes: String {
        notes ?? ""
    }
    
    public var formattedDateCreated: String {
        guard let dateCreated = dateCreated else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: dateCreated)
    }
}

extension BusinessCard {
    override public func awakeFromInsert() {
        super.awakeFromInsert()
        
        if id == nil {
            id = UUID()
        }
        
        if dateCreated == nil {
            dateCreated = Date()
        }
        
        dateModified = Date()
    }
    
    override public func willSave() {
        super.willSave()
        dateModified = Date()
    }
}
import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            BusinessCardsListView()
                .tabItem {
                    Image(systemName: "person.crop.rectangle.stack")
                    Text("Cards")
                }
                .tag(0)
            
            CategoriesView()
                .tabItem {
                    Image(systemName: "folder")
                    Text("Categories")
                }
                .tag(1)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(2)
        }
    }
}

struct BusinessCardsListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \BusinessCard.name, ascending: true)],
        animation: .default)
    private var businessCards: FetchedResults<BusinessCard>
    
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Category.name, ascending: true)],
        animation: .default)
    private var categories: FetchedResults<Category>
    
    @State private var searchText = ""
    @State private var selectedCategory: Category?
    @State private var showingAddCard = false
    @State private var showingCategoryFilter = false
    @State private var showingScanView = false
    @State private var showingExportView = false
    
    var filteredCards: [BusinessCard] {
        if searchText.isEmpty && selectedCategory == nil {
            return Array(businessCards)
        } else {
            return businessCards.filter { card in
                let matchesSearch = searchText.isEmpty || 
                    card.name?.localizedCaseInsensitiveContains(searchText) == true ||
                    card.email?.localizedCaseInsensitiveContains(searchText) == true ||
                    card.company?.localizedCaseInsensitiveContains(searchText) == true
                
                let matchesCategory = selectedCategory == nil || card.category == selectedCategory
                
                return matchesSearch && matchesCategory
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                SearchBar(text: $searchText)
                
                if selectedCategory != nil {
                    categoryFilterBar
                }
                
                List {
                    ForEach(filteredCards, id: \.self) { card in
                        NavigationLink(destination: BusinessCardDetailView(card: card)) {
                            BusinessCardRowView(card: card)
                        }
                    }
                    .onDelete(perform: deleteCards)
                }
            }
            .navigationTitle("Business Cards")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        Button(action: {
                            showingCategoryFilter = true
                        }) {
                            Label("Filter by Category", systemImage: "line.3.horizontal.decrease.circle")
                        }
                        
                        Button(action: {
                            showingExportView = true
                        }) {
                            Label("Export Cards", systemImage: "square.and.arrow.up")
                        }
                        .disabled(businessCards.isEmpty)
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: {
                            showingAddCard = true
                        }) {
                            Label("Add Manually", systemImage: "plus")
                        }
                        
                        Button(action: {
                            showingScanView = true
                        }) {
                            Label("Scan Business Card", systemImage: "viewfinder")
                        }
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddCard) {
                AddBusinessCardView()
            }
            .sheet(isPresented: $showingScanView) {
                ScanBusinessCardView()
            }
            .sheet(isPresented: $showingExportView) {
                ExportView(businessCards: Array(businessCards))
            }
            .actionSheet(isPresented: $showingCategoryFilter) {
                ActionSheet(
                    title: Text("Filter by Category"),
                    buttons: categoryFilterButtons()
                )
            }
        }
    }
    
    private var categoryFilterBar: some View {
        HStack {
            HStack {
                Circle()
                    .fill(selectedCategory?.categoryColor ?? .gray)
                    .frame(width: 12, height: 12)
                Text(selectedCategory?.wrappedName ?? "All")
                    .font(.subheadline)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(16)
            
            Spacer()
            
            Button("Clear") {
                selectedCategory = nil
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
    
    private func categoryFilterButtons() -> [ActionSheet.Button] {
        var buttons: [ActionSheet.Button] = []
        
        buttons.append(.default(Text("All Cards")) {
            selectedCategory = nil
        })
        
        for category in categories {
            buttons.append(.default(Text(category.wrappedName)) {
                selectedCategory = category
            })
        }
        
        buttons.append(.cancel())
        
        return buttons
    }

    private func deleteCards(offsets: IndexSet) {
        withAnimation {
            offsets.map { filteredCards[$0] }.forEach(viewContext.delete)
            
            do {
                try viewContext.save()
            } catch {
                let nsError = error as NSError
                print("Failed to delete cards: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("Search cards...", text: $text)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
        .padding(.horizontal)
    }
}

struct BusinessCardRowView: View {
    let card: BusinessCard
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(card.name ?? "Unknown")
                .font(.headline)
            
            if let company = card.company {
                Text(company)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            if let email = card.email {
                Text(email)
                    .font(.caption)
                    .foregroundColor(.blue)
            }
            
            if let phone = card.phone {
                Text(phone)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 2)
    }
}

struct AddBusinessCardView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) var presentationMode
    
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Category.name, ascending: true)],
        animation: .default)
    private var categories: FetchedResults<Category>
    
    @State private var name = ""
    @State private var company = ""
    @State private var email = ""
    @State private var phone = ""
    @State private var address = ""
    @State private var website = ""
    @State private var notes = ""
    @State private var selectedCategory: Category?
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Basic Information")) {
                    TextField("Name", text: $name)
                    TextField("Company", text: $company)
                    TextField("Email", text: $email)
                        .keyboardType(.emailAddress)
                    TextField("Phone", text: $phone)
                        .keyboardType(.phonePad)
                }
                
                Section(header: Text("Category")) {
                    Picker("Category", selection: $selectedCategory) {
                        Text("None").tag(nil as Category?)
                        ForEach(categories, id: \.self) { category in
                            HStack {
                                Circle()
                                    .fill(category.categoryColor)
                                    .frame(width: 12, height: 12)
                                Text(category.wrappedName)
                            }
                            .tag(category as Category?)
                        }
                    }
                }
                
                Section(header: Text("Additional Information")) {
                    TextField("Website", text: $website)
                        .keyboardType(.URL)
                    TextField("Address", text: $address)
                    TextField("Notes", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("Add Business Card")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveCard()
                    }
                    .disabled(name.isEmpty)
                }
            }
            .onAppear {
                if categories.isEmpty {
                    Category.createDefaultCategories(in: viewContext)
                }
            }
        }
    }
    
    private func saveCard() {
        withAnimation {
            let newCard = BusinessCard(context: viewContext)
            newCard.name = name
            newCard.company = company
            newCard.email = email
            newCard.phone = phone
            newCard.address = address
            newCard.website = website
            newCard.notes = notes
            newCard.category = selectedCategory
            
            do {
                try viewContext.save()
                presentationMode.wrappedValue.dismiss()
            } catch {
                let nsError = error as NSError
                print("Failed to save card: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
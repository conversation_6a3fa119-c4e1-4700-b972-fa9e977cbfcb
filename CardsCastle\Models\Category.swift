import Foundation
import CoreData
import Swift<PERSON>

@objc(Category)
public class Category: NSManagedObject {
    
}

extension Category {
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Category> {
        return NSFetchRequest<Category>(entityName: "Category")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var color: String?
    @NSManaged public var dateCreated: Date?
    @NSManaged public var businessCards: NSSet?
}

extension Category : Identifiable {
    public var wrappedName: String {
        name ?? "Uncategorized"
    }
    
    public var wrappedColor: String {
        color ?? "blue"
    }
    
    public var businessCardsArray: [BusinessCard] {
        let set = businessCards as? Set<BusinessCard> ?? []
        return set.sorted {
            $0.wrappedName < $1.wrappedName
        }
    }
    
    public var cardCount: Int {
        businessCardsArray.count
    }
    
    public var categoryColor: Color {
        switch wrappedColor {
        case "red":
            return .red
        case "green":
            return .green
        case "blue":
            return .blue
        case "orange":
            return .orange
        case "purple":
            return .purple
        case "yellow":
            return .yellow
        case "pink":
            return .pink
        default:
            return .blue
        }
    }
}

extension Category {
    @objc(addBusinessCardsObject:)
    @NSManaged public func addToBusinessCards(_ value: BusinessCard)

    @objc(removeBusinessCardsObject:)
    @NSManaged public func removeFromBusinessCards(_ value: BusinessCard)

    @objc(addBusinessCards:)
    @NSManaged public func addToBusinessCards(_ values: NSSet)

    @objc(removeBusinessCards:)
    @NSManaged public func removeFromBusinessCards(_ values: NSSet)
}

extension Category {
    override public func awakeFromInsert() {
        super.awakeFromInsert()
        
        if id == nil {
            id = UUID()
        }
        
        if dateCreated == nil {
            dateCreated = Date()
        }
        
        if color == nil {
            color = "blue"
        }
    }
    
    static func createDefaultCategories(in context: NSManagedObjectContext) {
        let defaultCategories = [
            ("Clients", "blue"),
            ("Vendors", "green"),
            ("Networking Contacts", "orange"),
            ("Prospects", "purple")
        ]
        
        for (name, color) in defaultCategories {
            let category = Category(context: context)
            category.name = name
            category.color = color
        }
        
        do {
            try context.save()
        } catch {
            print("Failed to create default categories: \(error)")
        }
    }
}